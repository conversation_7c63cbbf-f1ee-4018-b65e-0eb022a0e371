:root {
    --primary: #0a192f;
    --secondary: #112240;
    --accent: #64ffda;
    --text-primary: #e6f1ff;
    --text-secondary: #8892b0;
    --neon-glow: 0 0 10px var(--accent), 0 0 20px var(--accent), 0 0 30px var(--accent);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: var(--primary);
    color: var(--text-primary);
    font-family: 'Exo 2', sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
    background-image: 
        radial-gradient(circle at 10% 20%, rgba(28, 58, 107, 0.15) 0%, transparent 20%),
        radial-gradient(circle at 90% 80%, rgba(28, 58, 107, 0.15) 0%, transparent 20%);
    background-attachment: fixed;
}

/* Particle canvas for background */
#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: -1;
}

/* Header and Navigation */
header {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 20px 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background: rgba(10, 25, 47, 0.8);
    border-bottom: 1px solid rgba(100, 255, 218, 0.1);
}

.logo {
    font-family: 'Orbitron', sans-serif;
    font-weight: 700;
    font-size: 28px;
    color: var(--accent);
    text-shadow: var(--neon-glow);
    letter-spacing: 1px;
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links a {
    color: var(--text-primary);
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: var(--accent);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 0 10%;
    position: relative;
}

.hero-content {
    max-width: 800px;
    z-index: 1;
}

.hero-subtitle {
    color: var(--accent);
    font-family: 'Orbitron', sans-serif;
    font-size: 20px;
    margin-bottom: 20px;
    text-shadow: var(--neon-glow);
    animation: pulse 2s infinite;
}

.hero-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 60px;
    margin-bottom: 20px;
    line-height: 1.2;
    background: linear-gradient(45deg, var(--text-primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 15px rgba(100, 255, 218, 0.5);
}

.hero-description {
    font-size: 20px;
    margin-bottom: 40px;
    color: var(--text-secondary);
    max-width: 600px;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    margin-top: 30px;
}

.btn {
    padding: 12px 30px;
    border: 2px solid var(--accent);
    border-radius: 4px;
    background: transparent;
    color: var(--accent);
    font-family: 'Orbitron', sans-serif;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--accent);
    transition: all 0.4s ease;
    z-index: -1;
}

.btn:hover::before {
    left: 0;
}

.btn:hover {
    color: var(--primary);
    box-shadow: var(--neon-glow);
}

.btn-primary {
    background: var(--accent);
    color: var(--primary);
}

.btn-primary::before {
    background: var(--text-primary);
}

/* Section Styling */
section {
    padding: 100px 10%;
}

.section-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 36px;
    margin-bottom: 60px;
    text-align: center;
    color: var(--accent);
    text-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: var(--accent);
    box-shadow: var(--neon-glow);
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.about-text {
    font-size: 18px;
    color: var(--text-secondary);
}

.skills-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.skill {
    background: var(--secondary);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(100, 255, 218, 0.1);
}

.skill:hover {
    transform: translateY(-5px);
    border-color: var(--accent);
    box-shadow: var(--neon-glow);
}

.skill i {
    font-size: 40px;
    color: var(--accent);
    margin-bottom: 10px;
}

/* Projects Section */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

.project-card {
    background: var(--secondary);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(100, 255, 218, 0.1);
    position: relative;
}

.project-card:hover {
    transform: translateY(-10px);
    border-color: var(--accent);
    box-shadow: var(--neon-glow);
}

.project-img {
    width: 100%;
    height: 200px;
    background: linear-gradient(45deg, #0a192f, #112240);
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-img i {
    font-size: 60px;
    color: var(--accent);
}

.project-info {
    padding: 20px;
}

.project-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 22px;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.project-desc {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

/* Blog Section */
.blog-posts {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

.blog-card {
    background: var(--secondary);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(100, 255, 218, 0.1);
}

.blog-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent);
    box-shadow: var(--neon-glow);
}

.blog-content {
    padding: 20px;
}

.blog-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 22px;
    margin-bottom: 10px;
}

.blog-excerpt {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.contact-item i {
    font-size: 24px;
    color: var(--accent);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-family: 'Orbitron', sans-serif;
    color: var(--accent);
}

.form-group input,
.form-group textarea {
    padding: 12px 15px;
    background: var(--secondary);
    border: 1px solid rgba(100, 255, 218, 0.1);
    border-radius: 4px;
    color: var(--text-primary);
    font-family: 'Exo 2', sans-serif;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 10px rgba(100, 255, 218, 0.2);
}

/* Footer */
footer {
    background: var(--secondary);
    padding: 30px 10%;
    text-align: center;
    border-top: 1px solid rgba(100, 255, 218, 0.1);
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid var(--accent);
    color: var(--accent);
    font-size: 20px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--accent);
    color: var(--primary);
    box-shadow: var(--neon-glow);
}

.copyright {
    color: var(--text-secondary);
}

/* Animations */
@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.floating {
    animation: float 5s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 992px) {
    .about-content, .contact-content {
        grid-template-columns: 1fr;
    }
    
    .hero-title {
        font-size: 48px;
    }
}

@media (max-width: 768px) {
    header {
        padding: 20px;
    }
    
    .nav-links {
        display: none;
    }
    
    .hero-title {
        font-size: 36px;
    }
    
    section {
        padding: 80px 5%;
    }
    
    .projects-grid, .blog-posts {
        grid-template-columns: 1fr;
    }
}