// Supabase configuration
const SUPABASE_URL = 'http://localhost:8000';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE';

// Initialize Supabase client
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Initialize particles.js
particlesJS('particles-js', {
    "particles": {
        "number": {
            "value": 100,
            "density": {
                "enable": true,
                "value_area": 800
            }
        },
        "color": {
            "value": "#64ffda"
        },
        "shape": {
            "type": "circle",
            "stroke": {
                "width": 0,
                "color": "#000000"
            }
        },
        "opacity": {
            "value": 0.5,
            "random": true,
            "anim": {
                "enable": true,
                "speed": 1,
                "opacity_min": 0.1,
                "sync": false
            }
        },
        "size": {
            "value": 3,
            "random": true,
            "anim": {
                "enable": true,
                "speed": 2,
                "size_min": 0.1,
                "sync": false
            }
        },
        "line_linked": {
            "enable": true,
            "distance": 150,
            "color": "#64ffda",
            "opacity": 0.4,
            "width": 1
        },
        "move": {
            "enable": true,
            "speed": 2,
            "direction": "none",
            "random": true,
            "straight": false,
            "out_mode": "out",
            "bounce": false,
            "attract": {
                "enable": false,
                "rotateX": 600,
                "rotateY": 1200
            }
        }
    },
    "interactivity": {
        "detect_on": "canvas",
        "events": {
            "onhover": {
                "enable": true,
                "mode": "grab"
            },
            "onclick": {
                "enable": true,
                "mode": "push"
            },
            "resize": true
        },
        "modes": {
            "grab": {
                "distance": 140,
                "line_linked": {
                    "opacity": 1
                }
            },
            "push": {
                "particles_nb": 4
            }
        }
    },
    "retina_detect": true
});

// Simple scroll animation
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});

// Blog functionality
async function loadBlogPosts() {
    try {
        const { data: posts, error } = await supabase
            .from('blog_posts')
            .select('*')
            .order('created_at', { ascending: false });

        if (error) throw error;

        const blogContainer = document.querySelector('.blog-posts');
        if (!blogContainer) return;

        blogContainer.innerHTML = '';

        if (posts.length === 0) {
            blogContainer.innerHTML = '<p>No blog posts found.</p>';
            return;
        }

        posts.forEach(post => {
            const blogCard = document.createElement('div');
            blogCard.className = 'blog-card';
            blogCard.innerHTML = `
                <div class="blog-content">
                    <h3 class="blog-title">${post.title}</h3>
                    <p class="blog-excerpt">${post.excerpt || post.content.substring(0, 150) + '...'}</p>
                    <a href="blog-post.html?slug=${post.slug}" class="btn">Read More</a>
                </div>
            `;
            blogContainer.appendChild(blogCard);
        });
    } catch (error) {
        console.error('Error loading blog posts:', error);
        const blogContainer = document.querySelector('.blog-posts');
        if (blogContainer) {
            blogContainer.innerHTML = '<p>Error loading blog posts. Please try again later.</p>';
        }
    }
}

async function loadBlogPost() {
    const urlParams = new URLSearchParams(window.location.search);
    const slug = urlParams.get('slug');

    if (!slug) {
        document.querySelector('.hero-title').textContent = 'Post Not Found';
        document.querySelector('.hero-description').textContent = 'The requested blog post could not be found.';
        return;
    }

    try {
        const { data: post, error } = await supabase
            .from('blog_posts')
            .select('*')
            .eq('slug', slug)
            .single();

        if (error || !post) throw new Error('Post not found');

        document.querySelector('.hero-title').textContent = post.title;
        document.querySelector('.hero-description').textContent = post.excerpt || post.content.substring(0, 200) + '...';

        const contentContainer = document.querySelector('.blog-content');
        if (contentContainer) {
            contentContainer.innerHTML = `
                <p>${post.content}</p>
                <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid rgba(100, 255, 218, 0.1);">
                    <a href="blog.html" class="btn" style="display: inline-block;">← Back to Blog</a>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading blog post:', error);
        document.querySelector('.hero-title').textContent = 'Post Not Found';
        document.querySelector('.hero-description').textContent = 'The requested blog post could not be found.';
    }
}

// Contact form functionality
async function handleContactForm(event) {
    event.preventDefault();

    const form = event.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    // Disable button and show loading
    submitBtn.disabled = true;
    submitBtn.textContent = 'Sending...';

    try {
        const formData = new FormData(form);
        const contactData = {
            name: formData.get('name'),
            email: formData.get('email'),
            message: formData.get('message')
        };

        const { error } = await supabase
            .from('contacts')
            .insert([contactData]);

        if (error) throw error;

        // Success
        alert('Thank you for your message! I\'ll get back to you soon.');
        form.reset();

    } catch (error) {
        console.error('Error sending message:', error);
        alert('Sorry, there was an error sending your message. Please try again.');
    } finally {
        // Re-enable button
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
}

// Initialize functionality based on current page
document.addEventListener('DOMContentLoaded', function() {
    // Load blog posts if on blog page
    if (document.querySelector('.blog-posts')) {
        loadBlogPosts();
    }

    // Load specific blog post if on blog-post page
    if (window.location.pathname.includes('blog-post.html')) {
        loadBlogPost();
    }

    // Handle contact form submission
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }
});