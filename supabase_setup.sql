-- Create blog_posts table
CREATE TABLE IF NOT EXISTS blog_posts (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  excerpt TEXT,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create contacts table
CREATE TABLE IF NOT EXISTS contacts (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

-- Create policies for public access
CREATE POLICY "Public read access for blog_posts" ON blog_posts
  FOR SELECT USING (true);

CREATE POLICY "Public insert access for contacts" ON contacts
  FOR INSERT WITH CHECK (true);

-- Insert sample blog posts
INSERT INTO blog_posts (title, slug, excerpt, content) VALUES
('Building CLI Tools in Python', 'building-cli-tools-python',
 'Learn how to create efficient command-line interfaces using Python''s argparse and click libraries.',
 'Command-line interfaces (CLIs) are an essential part of many software tools, providing a powerful way to interact with programs through text-based commands. Python offers several libraries for building CLIs, with argparse and click being two of the most popular.

The argparse module is part of Python''s standard library, making it readily available without installing additional packages. It''s well-suited for simple to moderately complex CLI applications.

Click is a third-party library that provides a more declarative approach to building CLIs. It''s particularly useful for complex applications with many commands and options.

Both libraries handle help text generation, argument validation, and error handling, allowing you to focus on implementing your application''s core functionality.'),

('AI Winter Preparation', 'ai-winter-preparation',
 'Strategies for maintaining AI projects during challenging times and resource constraints.',
 'During periods of reduced funding and interest in AI (commonly called "AI winters"), it''s crucial to maintain your projects and skills. Here are some strategies:

1. Focus on core fundamentals
2. Build practical applications
3. Contribute to open source
4. Network with the community
5. Keep learning and experimenting

Remember that AI winters are temporary, and the field always rebounds stronger.'),

('C++ for AI Applications', 'cpp-ai-applications',
 'Exploring the performance benefits of using C++ in high-performance AI systems.',
 'C++ remains a powerful choice for AI applications that require maximum performance. Its ability to manage memory efficiently and execute code close to the hardware makes it ideal for:

- Real-time AI systems
- Embedded AI applications
- High-performance computing
- Game AI
- Computer vision pipelines

While Python is great for prototyping, C++ shines in production environments where every millisecond counts.'),

('Neural Network Optimization Techniques', 'neural-network-optimization',
 'Advanced methods for optimizing neural network performance and reducing computational overhead.',
 'Optimizing neural networks is crucial for deploying AI models in production. Key techniques include:

- Quantization
- Pruning
- Knowledge distillation
- Efficient architectures
- Hardware acceleration

These methods can significantly reduce model size and inference time while maintaining accuracy.'),

('Machine Learning Model Deployment', 'ml-model-deployment',
 'Best practices for deploying machine learning models in production environments with Docker and Kubernetes.',
 'Deploying ML models to production requires careful consideration of:

- Containerization with Docker
- Orchestration with Kubernetes
- Model versioning
- A/B testing
- Monitoring and logging
- Scalability considerations

Following best practices ensures reliable and maintainable model deployments.'),

('Ethical Considerations in AI Development', 'ethical-ai-development',
 'Examining the ethical implications of AI systems and how developers can build responsible AI solutions.',
 'As AI systems become more powerful, ethical considerations become increasingly important. Key areas include:

- Bias and fairness
- Privacy and data protection
- Transparency and explainability
- Safety and robustness
- Societal impact

Developers have a responsibility to consider these factors throughout the development lifecycle.');